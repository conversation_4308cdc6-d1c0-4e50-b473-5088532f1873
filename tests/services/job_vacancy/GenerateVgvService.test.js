const GenerateVgvService = require('../../../src/services/JobVacancy/GenerateVgvService');

describe('GenerateVgvService', () => {
  let service;
  let mockGoogleAiService;
  let mockOnetService;

  beforeEach(() => {
    mockGoogleAiService = {
      generateContent: jest.fn(),
    };

    mockOnetService = {
      getOccupations: jest.fn(),
      getKnowledges: jest.fn(),
      getSkills: jest.fn(),
      getAbilities: jest.fn(),
      getInterests: jest.fn(),
      getWorkValues: jest.fn(),
      getWorkStyles: jest.fn(),
    };

    service = new GenerateVgvService({
      googleAiService: mockGoogleAiService,
      onetService: mockOnetService,
    });

    // Reset all mocks
    jest.clearAllMocks();
  });

  describe('constructor', () => {
    it('should initialize with googleAiService and onetService', () => {
      expect(service).toBeInstanceOf(GenerateVgvService);
    });
  });

  describe('generateVgvRecords', () => {
    it('should choose AI-based approach when detailed_descriptions are available', async () => {
      const jobVacancy = {
        id: 1,
        name: 'HR Officer',
        detailed_descriptions: {
          key_responsibilities: ['record-keeping', 'coordinate interviews'],
          competencies: ['attention to detail', 'communication'],
          qualifications: ['labor law', 'MS Office/HRIS'],
        },
      };

      // Mock the private method to avoid database calls
      const mockVgvRecords = [
        { job_vacancy_id: 1, job_group_variable_id: 1, weight: 0.3, default_weight: 0.3 },
        { job_vacancy_id: 1, job_group_variable_id: 2, weight: 0.2, default_weight: 0.2 },
      ];

      jest.spyOn(service, '_GenerateVgvService__generateVgvRecordsWithAi').mockResolvedValue(mockVgvRecords);

      const result = await service.generateVgvRecords({ jobVacancy, boneId: 1 });

      expect(result).toEqual(mockVgvRecords);
      expect(service._GenerateVgvService__generateVgvRecordsWithAi).toHaveBeenCalledWith({ jobVacancy, boneId: 1 });
    });

    it('should choose KSAO-based approach when detailed_descriptions are not available', async () => {
      const jobVacancy = {
        id: 1,
        name: 'Software Engineer',
        ksao: {
          skills: ['programming', 'debugging'],
          abilities: ['problem solving', 'analytical thinking'],
          knowledges: ['computer science', 'software engineering'],
          other_characteristics: ['attention to detail', 'teamwork'],
        },
      };

      // Mock the private method to avoid database calls
      const mockVgvRecords = [
        { job_vacancy_id: 1, job_group_variable_id: 1, weight: 0.5, default_weight: 0.5 },
        { job_vacancy_id: 1, job_group_variable_id: 2, weight: 0.5, default_weight: 0.5 },
      ];

      jest.spyOn(service, '_GenerateVgvService__generateVgvRecordsWithKsao').mockResolvedValue(mockVgvRecords);

      const result = await service.generateVgvRecords({ jobVacancy, boneId: 1 });

      expect(result).toEqual(mockVgvRecords);
      expect(service._GenerateVgvService__generateVgvRecordsWithKsao).toHaveBeenCalledWith({
        ksao: jobVacancy.ksao,
        jobVacancyId: 1,
        boneId: 1
      });
    });


  });
});
