Of course. Here is a rewritten version of your technical document, structured for clarity, professionalism, and easy implementation. The content and logic are preserved, but presented in a more formal and organized manner.

---

### **Workflow: Deriving Competency Weights from Job Descriptions**

This document outlines a hybrid workflow for analyzing Job Descriptions (JDs) to derive competency weights. The system leverages a Large Language Model (LLM) for qualitative text analysis while relying on a deterministic, rules-based engine for all quantitative calculations. This approach ensures high levels of reproducibility, explainability, and auditability.

**Core Principle:**
*   **AI (LLM):** Used exclusively to map phrases from a JD to one of eight predefined Group Variables (GVs). The AI acts as a sophisticated text classifier.
*   **Rules-Based Engine:** Handles all numerical calculations, including scoring, aggregation, smoothing, and normalization.

The workflow is detailed below, covering system architecture, prompt engineering, calculation formulas, a simulation, and best practices.

***

### 1. System Architecture

The architecture is designed to separate linguistic interpretation (AI) from mathematical calculation (rules-based).

**Process Flow:**
1.  **Input:** A Job Description, pre-processed and separated into three sections: Key Responsibilities (KR), Skills & Competencies (SC), and Qualifications (Q).
2.  **AI Mapping (LLM):** The LLM processes the text from each section and outputs a structured JSON list. For each relevant phrase, the AI provides:
    *   The **Group Variable (GV)** label (1 of 8).
    *   A **strength** classification (strong/medium/light).
    *   A **confidence** score (0.0–1.0).
    *   The **evidence** (the original text snippet).
3.  **Rules-Based Calculation:** The structured JSON output is fed into a deterministic engine that performs the following steps:
    *   Calculates a raw score for each phrase based on section weight, strength, and confidence.
    *   Aggregates these scores for each of the eight Group Variables.
    *   Applies Dirichlet smoothing to ensure no GV has a weight of zero.
    *   Normalizes the final scores to produce the final JD weight vector ($w_{JD}$), where the sum of all GV weights is 1.
    *   (Optional) Applies a softmax function with a temperature parameter to control the distribution's sharpness.

**Rationale:** This hybrid model provides a clear separation of concerns. The LLM handles the nuanced task of understanding natural language, while the deterministic calculations ensure that the final weights are consistent, transparent, and fully auditable.

***

### 2. LLM Prompting Strategy

To ensure consistent and high-quality output, a structured prompt is used with deterministic settings (`temperature=0`, `top_p=1`) and a JSON-only output constraint.

#### 2.1. System Prompt (Constant)
This prompt sets the context, task, and output constraints for the LLM.

```
You are an expert annotator for talent analytics. Your task is to map phrases from a Job Description (JD) to exactly one of 8 Group Variables (GVs).

Output must be in JSON format only, strictly following the provided schema. Use direct quotes from the JD as evidence; do not invent or paraphrase text.

**Group Variables (GVs) - Choose one per phrase:**
1.  **Conscientiousness & Reliability (CR):** Accuracy, detail-orientation, documentation, compliance, record-keeping, time management, orderliness.
2.  **Social Orientation & Collaboration (SOC):** Coordination, communication, teamwork, stakeholder interaction, customer service.
3.  **Cognitive Complexity & Problem-Solving (CCPS):** Analysis, reasoning, troubleshooting, evaluation, data-driven decision-making.
4.  **Adaptability & Stress Tolerance (AST):** Working under pressure, managing deadlines, flexibility, change readiness, handling ambiguity.
5.  **Leadership & Influence (LI):** Leading teams, guiding, influencing, facilitating, mentoring.
6.  **Motivation & Drive (MD):** Initiative, proactivity, target orientation, achievement-driven, ownership.
7.  **Creativity & Innovation Orientation (CIO):** Ideation, design, process improvement, experimentation.
8.  **Cultural & Values Fit (CVF):** Adherence to ethics, policy, regulations, labor law, company values.

**Strength Scale:**
- "strong": An exact or near-exact competency term is present (e.g., "attention to detail," "compliance").
- "medium": A clear synonym or descriptive phrase is used (e.g., "maintain accurate records").
- "light": A weak or general cue is identified (e.g., "generates reports"), used only if clearly relevant.

**Confidence:**
- A score from 0.00 to 1.00. If uncertain, select the most plausible GV and assign a lower confidence score.
```

#### 2.2. User Prompt (Template)
This prompt provides the specific JD content and section weights for processing.

```json
{
  "JOB_TITLE": "<Insert Job Title Here>",
  "SECTION_WEIGHTS": {"KR": 0.40, "SC": 0.40, "Q": 0.20},
  "JD_SECTIONS": [
    {"section": "KR", "text": "<Paste Key Responsibilities text here>"},
    {"section": "SC", "text": "<Paste Skills & Competencies text here>"},
    {"section": "Q", "text": "<Paste Qualifications text here>"}
  ]
}

Return a single JSON object with this schema:
{
  "job_title": string,
  "phrases": [
    {
      "section": "KR"|"SC"|"Q",
      "phrase": string,          // The exact evidence text from the JD
      "gv": "CR"|"SOC"|"CCPS"|"AST"|"LI"|"MD"|"CIO"|"CVF",
      "strength": "strong"|"medium"|"light",
      "confidence": number       // A float from 0.0 to 1.0
    },
    ...
  ]
}
Return only the JSON object. Do not include any extra text, comments, or explanations.
```

***

### 3. From AI Output to Numerical Weights

Once the LLM returns the structured JSON, the following formulas are applied.

**1. Define Weights:**
*   **Section Weights ($m_{section}$):** Tunable per role. A typical default is:
    $m_{KR} = 0.40, \; m_{SC} = 0.40, \; m_{Q} = 0.20$
*   **Strength Weights:**
    `strong` = 1.0, `medium` = 0.6, `light` = 0.3

**2. Calculate Phrase Score:**
The score for each individual phrase ($i$) is calculated as:
$\text{score}(i) = m_{\text{section}(i)} \times \text{strength\_weight}(i) \times \text{confidence}(i)$
*(Note: A TF-IDF term can be added here—e.g., $\text{score} = m \times \text{tfidf} \times \text{strength}$—if a relevant corpus is available.)*

**3. Aggregate Scores by GV:**
The total raw score for each Group Variable ($g$) is the sum of scores of all phrases mapped to it:
$S_g = \sum_{i:\, gv(i)=g} \text{score}(i)$

**4. Normalize and Smooth Scores (Dirichlet Smoothing):**
To prevent any GV from having a zero weight, additive (Dirichlet) smoothing is applied. This ensures a base weight for all competencies.
$w^{JD}_g = \frac{S_g + \kappa\pi_g}{\sum_{h} (S_h + \kappa\pi_h)}$
*   Where $\pi_g = 1/8$ (a uniform prior for 8 GVs) and $\kappa$ is the smoothing factor (e.g., $\kappa \in [0.5, 1.0]$).

**5. (Optional) Softmax Temperature:**
For finer control over the weight distribution, a softmax function can be used instead of simple normalization:
$w^{JD}_g = \frac{e^{S_g/\tau}}{\sum_{h} e^{S_h/\tau}}$
*   Where $\tau$ is the temperature. A lower $\tau$ (<1.0) sharpens the distribution, while a higher $\tau$ (>1.0) softens it.

***

### 4. Example Simulation

**Input:** Abridged LLM output for an "HR Officer" role.

```json
[
 {"section":"KR","phrase":"record-keeping","gv":"CR","strength":"strong","confidence":0.95},
 {"section":"KR","phrase":"documentation","gv":"CR","strength":"medium","confidence":0.90},
 {"section":"KR","phrase":"coordinate interviews","gv":"SOC","strength":"strong","confidence":0.95},
 {"section":"KR","phrase":"candidate communications","gv":"SOC","strength":"strong","confidence":0.85},
 {"section":"KR","phrase":"compliance with labor regulations","gv":"CVF","strength":"strong","confidence":0.90},
 {"section":"SC","phrase":"attention to detail","gv":"CR","strength":"strong","confidence":0.98},
 {"section":"SC","phrase":"communication","gv":"SOC","strength":"medium","confidence":0.80},
 {"section":"SC","phrase":"problem solving","gv":"CCPS","strength":"strong","confidence":0.90},
 {"section":"SC","phrase":"teamwork","gv":"SOC","strength":"medium","confidence":0.85},
 {"section":"Q","phrase":"labor law","gv":"CVF","strength":"medium","confidence":0.85},
 {"section":"Q","phrase":"MS Office/HRIS","gv":"CR","strength":"medium","confidence":0.80},
 {"section":"SC","phrase":"adaptability","gv":"AST","strength":"strong","confidence":0.85}
]
```

**Step 1: Calculate Individual Phrase Scores**
(Using $m_{KR/SC}=0.40, m_Q=0.20$ and strength weights {1.0, 0.6, 0.3})
*   `CR`: 0.40×1.0×0.95=0.380; 0.40×0.6×0.90=0.216; 0.40×1.0×0.98=0.392; 0.20×0.6×0.80=0.096
*   `SOC`: 0.40×1.0×0.95=0.380; 0.40×1.0×0.85=0.340; 0.40×0.6×0.80=0.192; 0.40×0.6×0.85=0.204
*   `CVF`: 0.40×1.0×0.90=0.360; 0.20×0.6×0.85=0.102
*   `CCPS`: 0.40×1.0×0.90=0.360
*   `AST`: 0.40×1.0×0.85=0.340

**Step 2: Aggregate Scores by GV ($S_g$)**
*   **$S_{CR}$**: 0.380 + 0.216 + 0.392 + 0.096 = **1.084**
*   **$S_{SOC}$**: 0.380 + 0.340 + 0.192 + 0.204 = **1.116**
*   **$S_{CVF}$**: 0.360 + 0.102 = **0.462**
*   **$S_{CCPS}$**: **0.360**
*   **$S_{AST}$**: **0.340**
*   **$S_{LI}, S_{MD}, S_{CIO}$**: **0**
*   **Total Score $\sum S_h$**: 3.362

**Step 3: Apply Dirichlet Smoothing**
(Using $\kappa=0.8$, each GV gets an added score of $\kappa \times \pi_g = 0.8 \times 1/8 = 0.1$)
*   **Denominator**: $\sum S_h + \kappa = 3.362 + 0.8 = 4.162$
*   **Final Weights ($w_{JD}$):**
    *   **SOC:** (1.116 + 0.1) / 4.162 = **29.2%**
    *   **CR:** (1.084 + 0.1) / 4.162 = **28.5%**
    *   **CVF:** (0.462 + 0.1) / 4.162 = **13.5%**
    *   **CCPS:** (0.360 + 0.1) / 4.162 = **11.1%**
    *   **AST:** (0.340 + 0.1) / 4.162 = **10.6%**
    *   **LI / MD / CIO:** (0 + 0.1) / 4.162 = **2.4% each**

The final weights reflect the JD's emphasis while ensuring all competencies are represented.

***

### 5. Guardrails and Best Practices

*   **Determinism:** Use `temperature=0` in LLM calls. Enforce the strict JSON schema and limit labels to the 8 GVs to ensure predictable outputs.
*   **Auditability:** The `evidence` field in the JSON output is mandatory, linking every label back to the source text for easy review by Subject Matter Experts (SMEs).
*   **Confidence Thresholding:** Discard AI-generated phrases with confidence below a certain threshold (e.g., < 0.40) before aggregation to filter out low-quality mappings.
*   **Hybrid Dictionary:** Use the AI's outputs to progressively build and refine a keyword dictionary. Over time, this can reduce reliance on the LLM for common phrases.
*   **Bias and Drift Monitoring:** Periodically review outputs for potential biases and monitor performance as language and job roles evolve. Update prompts and definitions quarterly.
*   **Dual-Layer Validation:**
    *   **Intrinsic:** Have SMEs review the LLM's phrase-to-GV mappings for accuracy.
    *   **Extrinsic:** Backtest the final JD weights against historical performance data to validate their predictive power.

***

### 6. Summary of Default Parameters

*   **Section Weights:** KR: 0.40, SC: 0.40, Q: 0.20
*   **Strength Weights:** strong: 1.0, medium: 0.6, light: 0.3
*   **Confidence Threshold:** 0.40
*   **Smoothing ($\kappa$):** 0.8
*   **Softmax Temperature ($\tau$, optional):** 1.0