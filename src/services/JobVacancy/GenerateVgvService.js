const AppService = require('../AppService');
const { Bone, JobGroupVariable, LlmMetadata } = require('../../models');

// --- Constants ---
const ONET_DATA_KEYS = [
  'knowledges',
  'skills',
  'abilities',
  'interests',
  'work_values',
  'work_styles',
];

const BONE_MAPPINGS = {
  1: { Business: 7, Functional: 5, Professional: 6 },
  2: { Business: 6, Functional: 4, Professional: 3 },
  3: { Business: 5, Functional: 4, Professional: 7 },
  4: { Business: 6, Functional: 7, Professional: 5 },
  5: { Business: 7, Functional: 5, Professional: 5 },
  6: { Business: 7, Functional: 5, Professional: 6 },
  7: { Business: 7, Functional: 5, Professional: 4 },
  8: { Business: 6, Functional: 5, Professional: 5 },
};

const KSAO_SYSTEM_PROMPT = `
## Role and Context
You are an expert HR professional specializing in job analysis and competency modeling. Your expertise includes translating job requirements into structured Knowledge, Skills, Abilities, and Other characteristics (KSAO) frameworks that align with industry standards and O*NET occupational data.

## Task Overview
Create a comprehensive KSAO profile for a specific job vacancy by analyzing job descriptions and leveraging O*NET occupational data as supporting reference material.

## Input Structure
You will receive:
1. **Job Title**: The specific position title
2. **Job Descriptions**: Detailed list of key job responsibilities, qualifications, skill and competencies, and success metrics
3. **Related O*NET Data**: Occupational information including:
  - Knowledge areas
  - Skills requirements
  - Abilities needed
  - Work interests
  - Work values
  - Work styles

## Instructions

### Step 1: Analysis
- Carefully analyze the provided job descriptions to identify core responsibilities and requirements
- Cross-reference with O*NET data to identify relevant competencies
- Prioritize job-specific requirements while using O*NET data to fill gaps and validate decisions

### Step 2: KSAO Development
Create four distinct categories using these definitions and formatting guidelines:

**Knowledge (K)**: Body of facts and information someone must know
- Write as short, specific topic areas or subject matter domains
- Use concise phrases (3-8 words typically)
- Focus on what someone needs to know, not how they apply it
- Examples: "Brand positioning and messaging", "Employment law and regulations", "Financial analysis principles"

**Skills (S)**: Learned proficiencies that can be demonstrated
- Write as actionable capabilities or techniques
- Use verb phrases when appropriate
- Focus on what someone can do or perform
- Examples: "Develop brand strategy", "Conduct market research", "Data analysis and reporting"

**Abilities (A)**: Enduring attributes that support performance
- Write as inherent capacities or traits
- Often start with "Ability to..." but keep concise
- Focus on cognitive, physical, or interpersonal capabilities
- Examples: "Strategic thinking", "Ability to influence stakeholders", "Analytical reasoning"

**Other Characteristics (O)**: Traits, motivations, values, or work styles
- Write as personal attributes or orientations
- Focus on personality traits, work preferences, and motivational factors
- Examples: "Creative mindset", "Detail orientation", "Growth mindset"

### Step 3: Quality Standards
- Keep each item concise (typically 2-6 words, maximum 8 words)
- Ensure items are specific and job-relevant
- Avoid lengthy explanations or examples in the KSAO items themselves
- Maintain clear distinction between categories
- Aim for 6-10 items per category for comprehensive coverage
- Prioritize the most critical competencies for job success

## Output Format
Return your response as a valid JSON object with the following structure:
{
  "knowledges": ["string"],
  "skills": ["string"],
  "abilities": ["string"],
  "other_characteristics": ["string"]
}

## Important Notes
- Base your KSAO primarily on the job descriptions provided
- Use O*NET data as supporting reference to enhance and validate your analysis
- Keep all items concise and specific - avoid lengthy explanations
- Focus on the most essential competencies for job success
- Ensure all items are directly relevant to the specific job vacancy
- Maintain professional HR terminology and standards
- Double-check that your output is valid JSON format
`;

class GenerateVgvService extends AppService {
  #googleAiService;
  #onetService;

  constructor({ googleAiService, onetService }) {
    super();
    this.#googleAiService = googleAiService;
    this.#onetService = onetService;
  }

  async generateKsao(vacancy) {
    const {
      name: vacancyName,
      detailed_descriptions: jobDetails = {},
      job_desc: fallbackJobDesc,
      related_onetsoc_codes: onetsocCodes,
    } = vacancy;

    const jobDesc = jobDetails.key_responsibilities || fallbackJobDesc;
    const occupations = await this.#getOccupationsData(onetsocCodes);

    const userPrompt = await this.#buildUserPrompt(vacancyName, jobDesc, occupations, jobDetails);
    const aiParams = this.#buildAiParams(userPrompt, KSAO_SYSTEM_PROMPT);
    const response = await this.#googleAiService.generateContent(aiParams);

    this.#logLlmInteraction(aiParams, response, 'generate_ksao');

    return this.#parseAiResponse(response);
  }

  async generateVgvRecords({ ksao, jobVacancyId, boneId }) {
    const [jobGroupVariables, flattenedKsao, boneName] = await Promise.all([
      JobGroupVariable.findAll({
        attributes: ['id', 'keywords'],
      }),
      Object.values(ksao)
        .flat()
        .map(item => String(item).toLowerCase())
        .join(';'),
      boneId ? Bone.findByPk(boneId).then(bone => bone?.name) : null,
    ]);

    return jobGroupVariables.map(jgv => {
      const matchCount = jgv.keywords.reduce(
        (count, keyword) => (flattenedKsao.includes(keyword) ? count + 1 : count),
        0,
      );

      const boneMapping = BONE_MAPPINGS[jgv.id] || {};
      const boneValue = boneName ? boneMapping[boneName] || 0 : 0;

      return {
        job_vacancy_id: jobVacancyId,
        job_group_variable_id: jgv.id,
        keyword_match_count: matchCount,
        keyword_total_count: jgv.keywords.length,
        match_type: 'weight',
        weight: 1 / jobGroupVariables.length,
        bone_value: boneValue,
      };
    });
  }

  // ---------------
  // Private methods
  // ---------------

  async #getOccupationsData(onetsocCodes) {
    const getMethodName = key => {
      const snakeCaseName = `get_${key}`;
      const camelizedName = snakeCaseName.replace(/_([a-z])/g, g => g[1].toUpperCase());

      return camelizedName;
    };

    const dataPromises = ['occupations', ...ONET_DATA_KEYS].map(key => {
      const methodName = getMethodName(key);
      const dataPromise = this.#onetService[methodName](onetsocCodes);

      return dataPromise;
    });

    const [occupations, ...referenceDataSets] = await Promise.all(dataPromises);
    const referenceData = ONET_DATA_KEYS.reduce((acc, key, index) => {
      acc[key] = referenceDataSets[index];
      return acc;
    }, {});

    for (const [key, items] of Object.entries(referenceData)) {
      for (const item of items) {
        const { onetsoc_code, ...rest } = item;
        if (occupations[onetsoc_code]) {
          occupations[onetsoc_code][key] = occupations[onetsoc_code][key] || [];
          occupations[onetsoc_code][key].push(rest);
        }
      }
    }

    return occupations;
  }

  async #buildUserPrompt(vacancyName, jobDesc, occupations, jobDetails) {
    let userPrompt = `# Job Title\n${vacancyName}\n\n`;

    const mapJobDetails = array => {
      let result = '';
      array.forEach(item => {
        result += `- ${item}\n\n`;
      });
      return result;
    };

    if (jobDetails) {
      userPrompt += `# Job Details\n`;
      userPrompt += '## Job Key Responsibilities\n';
      userPrompt += mapJobDetails(jobDetails.key_responsibilities);
      userPrompt += '## Job Qualifications\n';
      userPrompt += mapJobDetails(jobDetails.qualifications);
      userPrompt += '## Job Skill & Competencies\n';
      userPrompt += mapJobDetails(jobDetails.competencies);
      userPrompt += '## Job Success Metrics\n';
      userPrompt += mapJobDetails(jobDetails.success_metrics);
    } else {
      userPrompt += `# Job Descriptions\n`;
      userPrompt += mapJobDetails(jobDesc);
    }

    userPrompt += '\n# Related O*NET Data\n';

    await Promise.all(
      Object.values(occupations).map(async occupation => {
        userPrompt += `## Occupation: ${occupation.title}\n`;
        userPrompt += `### Description\n${occupation.description}\n\n`;

        await Promise.all(
          ONET_DATA_KEYS.map(async key => {
            if (occupation[key] && occupation[key].length > 0) {
              userPrompt += `### ${key.replace('_', ' ')}\n`;
              userPrompt += `\`\`\`json\n`;
              userPrompt += JSON.stringify(occupation[key], null, 2);
              userPrompt += `\n\`\`\`\n\n`;
            }
          }),
        );
      }),
    );

    return Promise.resolve(userPrompt);
  }

  #buildAiParams(userPrompt, systemPrompt) {
    return {
      model: 'gemini-2.5-flash',
      contents: [{ role: 'user', parts: [{ text: userPrompt }] }],
      config: {
        temperature: 0.2,
        responseMimeType: 'application/json',
        thinkingConfig: { thinkingBudget: -1 },
        systemInstruction: [{ text: systemPrompt }],
      },
    };
  }

  #logLlmInteraction(request, responses, actionType) {
    LlmMetadata.create({
      request,
      responses,
      action_type: actionType,
    }).catch(error => {
      console.error('Failed to log LLM interaction:', error);
    });
  }

  #parseAiResponse(response) {
    try {
      const content = response?.candidates?.[0]?.content?.parts?.[0]?.text;
      if (content) {
        return JSON.parse(content);
      }

      throw new Error('Invalid AI response structure. No content found.');
    } catch (error) {
      console.error(
        'Failed to parse AI response:',
        error,
        'Raw content:',
        response?.candidates?.[0]?.content?.parts?.[0]?.text,
      );

      throw new Error('Could not parse KSAO data from AI response.');
    }
  }
}

module.exports = GenerateVgvService;
