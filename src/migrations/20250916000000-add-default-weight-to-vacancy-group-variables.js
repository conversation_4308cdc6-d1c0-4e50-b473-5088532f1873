'use strict';

/** @type {import('sequelize-cli').Migration} */
module.exports = {
  async up(queryInterface, Sequelize) {
    await queryInterface.addColumn('vacancy_group_variables', 'default_weight', {
      type: Sequelize.FLOAT,
      allowNull: true,
    });
  },

  async down(queryInterface, _Sequelize) {
    await queryInterface.removeColumn('vacancy_group_variables', 'default_weight');
  },
};
