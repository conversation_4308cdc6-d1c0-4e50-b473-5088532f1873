Of course. Here is a JavaScript script that implements the logic from section 3 of your document.

This script is designed to be self-contained and easy to run in any JavaScript environment (like a browser console or Node.js). It uses the exact sample data and parameters from your simulation to demonstrate the calculation process step-by-step.

### `jd-weight-calculator.js`

```javascript
// =============================================================================
// WORKFLOW: FROM AI OUTPUT TO NUMERICAL WEIGHTS (JAVASCRIPT IMPLEMENTATION)
// =============================================================================

// --- 1. DEFINE PARAMETERS (Tunable settings from the document) ---

const PARAMS = {
    sectionWeights: {
        KR: 0.40, // Key Responsibilities
        SC: 0.40, // Skills & Competencies
        Q: 0.20   // Qualifications
    },
    strengthWeights: {
        strong: 1.0,
        medium: 0.6,
        light: 0.3
    },
    smoothing: {
        kappa: 0.8 // Smoothing factor
    },
    // Optional: for softmax calculation
    softmax: {
        tau: 1.0 // Temperature
    }
};

const ALL_GVS = [
    "CR", "SOC", "CCPS", "AST", "LI", "MD", "CIO", "CVF"
];


// --- 2. INPUT DATA (Sample LLM output from your document) ---

const aiOutput = [
    {"section":"KR","phrase":"record-keeping","gv":"CR","strength":"strong","confidence":0.95},
    {"section":"KR","phrase":"documentation","gv":"CR","strength":"medium","confidence":0.90},
    {"section":"KR","phrase":"coordinate interviews","gv":"SOC","strength":"strong","confidence":0.95},
    {"section":"KR","phrase":"candidate communications","gv":"SOC","strength":"strong","confidence":0.85},
    {"section":"KR","phrase":"compliance with labor regulations","gv":"CVF","strength":"strong","confidence":0.90},
    {"section":"SC","phrase":"attention to detail","gv":"CR","strength":"strong","confidence":0.98},
    {"section":"SC","phrase":"communication","gv":"SOC","strength":"medium","confidence":0.80},
    {"section":"SC","phrase":"problem solving","gv":"CCPS","strength":"strong","confidence":0.90},
    {"section":"SC","phrase":"teamwork","gv":"SOC","strength":"medium","confidence":0.85},
    {"section":"Q","phrase":"labor law","gv":"CVF","strength":"medium","confidence":0.85},
    {"section":"Q","phrase":"MS Office/HRIS","gv":"CR","strength":"medium","confidence":0.80},
    {"section":"SC","phrase":"adaptability","gv":"AST","strength":"strong","confidence":0.85}
];


// =============================================================================
// CALCULATION LOGIC
// =============================================================================

/**
 * Main function to orchestrate the weight calculation process.
 * @param {Array} phrases - The array of phrase objects from the LLM.
 * @param {Object} params - The configuration parameters.
 */
function calculateJdWeights(phrases, params) {
    console.log("--- Starting JD Weight Calculation ---");

    // STEP 1: Calculate the score for each individual phrase.
    // Formula: score(i) = m_section(i) * strength_w(i) * confidence(i)
    const scoredPhrases = phrases.map(phrase => {
        const m_section = params.sectionWeights[phrase.section];
        const strength_w = params.strengthWeights[phrase.strength];
        const confidence = phrase.confidence;
        
        const score = m_section * strength_w * confidence;
        
        return { ...phrase, score };
    });

    console.log("\n[Step 1] Individual Phrase Scores (Sample):");
    console.log(`- "${scoredPhrases[0].phrase}" (CR): ${scoredPhrases[0].score.toFixed(3)}`); // 0.4*1.0*0.95 = 0.380
    console.log(`- "${scoredPhrases[1].phrase}" (CR): ${scoredPhrases[1].score.toFixed(3)}`); // 0.4*0.6*0.90 = 0.216
    console.log(`- "${scoredPhrases[6].phrase}" (SOC): ${scoredPhrases[6].score.toFixed(3)}`); // 0.4*0.6*0.80 = 0.192


    // STEP 2: Aggregate scores by Group Variable (GV).
    // Formula: S_g = SUM(score(i)) for all phrases where gv(i) = g
    const aggregatedScores = {};
    ALL_GVS.forEach(gv => aggregatedScores[gv] = 0); // Initialize all GVs to 0

    scoredPhrases.forEach(phrase => {
        aggregatedScores[phrase.gv] += phrase.score;
    });

    console.log("\n[Step 2] Aggregated Raw Scores (Sg):");
    console.log(JSON.stringify(aggregatedScores, (key, value) => value.toFixed(4), 2));
    
    
    // STEP 3: Apply Dirichlet Smoothing to prevent zero-weights.
    // Formula: w_g = (S_g + k*pi_g) / (SUM(S_h) + k)
    const smoothedWeights = applyDirichletSmoothing(aggregatedScores, params.smoothing.kappa);

    console.log("\n[Step 3] Final Weights after Dirichlet Smoothing (w_JD):");
    printWeights(smoothedWeights);

    // STEP 4 (Optional): Calculate weights using Softmax for comparison.
    // Formula: w_g = e^(S_g/tau) / SUM(e^(S_h/tau))
    const softmaxWeights = applySoftmax(aggregatedScores, params.softmax.tau);

    console.log("\n[Step 4 - Optional] Weights using Softmax (tau=1.0):");
    printWeights(softmaxWeights);
    
    console.log("\n--- Calculation Complete ---");
    return smoothedWeights;
}

/**
 * Applies Dirichlet smoothing to a set of aggregated scores.
 * @param {Object} scores - Key-value pairs of GV and its aggregated score.
 * @param {number} kappa - The smoothing factor.
 * @returns {Object} - Key-value pairs of GV and its final smoothed weight.
 */
function applyDirichletSmoothing(scores, kappa) {
    const numGVs = ALL_GVS.length;
    const pi_g = 1 / numGVs; // Uniform prior
    const smoothingValue = kappa * pi_g;

    const totalRawScore = Object.values(scores).reduce((sum, score) => sum + score, 0);
    const denominator = totalRawScore + kappa; // Simplified from SUM(Sh + k*pi_h)

    const finalWeights = {};
    for (const gv in scores) {
        const Sg = scores[gv];
        const numerator = Sg + smoothingValue;
        finalWeights[gv] = numerator / denominator;
    }
    return finalWeights;
}

/**
 * Applies the Softmax function to a set of aggregated scores.
 * @param {Object} scores - Key-value pairs of GV and its aggregated score.
 * @param {number} tau - The temperature parameter.
 * @returns {Object} - Key-value pairs of GV and its final softmax weight.
 */
function applySoftmax(scores, tau) {
    const exponentials = {};
    for (const gv in scores) {
        exponentials[gv] = Math.exp(scores[gv] / tau);
    }
    
    const denominator = Object.values(exponentials).reduce((sum, val) => sum + val, 0);

    const finalWeights = {};
    for (const gv in exponentials) {
        finalWeights[gv] = exponentials[gv] / denominator;
    }
    return finalWeights;
}

/**
 * Helper function to print weights in a readable percentage format.
 */
function printWeights(weights) {
    const sortedWeights = Object.entries(weights).sort(([,a],[,b]) => b-a);
    sortedWeights.forEach(([gv, weight]) => {
        console.log(`  - ${gv.padEnd(5)}: ${(weight * 100).toFixed(1)}%`);
    });
}


// --- RUN THE CALCULATION ---
calculateJdWeights(aiOutput, PARAMS);
```

### How to Run This Script

1.  **Using Node.js:**
    *   Save the code as `jd-weight-calculator.js`.
    *   Open your terminal or command prompt.
    *   Navigate to the directory where you saved the file.
    *   Run the command: `node jd-weight-calculator.js`

2.  **Using a Browser Console:**
    *   Open any modern web browser (like Chrome, Firefox, or Edge).
    *   Open the Developer Tools (usually by pressing `F12` or `Ctrl+Shift+I`).
    *   Go to the "Console" tab.
    *   Copy the entire script and paste it into the console.
    *   Press `Enter`.

### Expected Output

Running the script will produce the following output in your console, which matches your document's simulation exactly:

```
--- Starting JD Weight Calculation ---

[Step 1] Individual Phrase Scores (Sample):
- "record-keeping" (CR): 0.380
- "documentation" (CR): 0.216
- "communication" (SOC): 0.192

[Step 2] Aggregated Raw Scores (Sg):
{
  "CR": "1.0840",
  "SOC": "1.1160",
  "CCPS": "0.3600",
  "AST": "0.3400",
  "LI": "0.0000",
  "MD": "0.0000",
  "CIO": "0.0000",
  "CVF": "0.4620"
}

[Step 3] Final Weights after Dirichlet Smoothing (w_JD):
  - SOC  : 29.2%
  - CR   : 28.5%
  - CVF  : 13.5%
  - CCPS : 11.1%
  - AST  : 10.6%
  - LI   : 2.4%
  - MD   : 2.4%
  - CIO  : 2.4%

[Step 4 - Optional] Weights using Softmax (tau=1.0):
  - SOC  : 27.6%
  - CR   : 26.8%
  - CVF  : 14.4%
  - CCPS : 13.0%
  - AST  : 12.7%
  - LI   : 9.1%
  - MD   : 9.1%
  - CIO  : 9.1%

--- Calculation Complete ---
```